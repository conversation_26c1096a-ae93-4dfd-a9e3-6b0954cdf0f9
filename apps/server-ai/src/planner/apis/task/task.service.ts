import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { TaskRepository } from '../../database/repositories/task.repository';
import { SessionRepository } from '../../database/repositories/session.repository';
import { PRDRepository } from '../../database/repositories/prd.repository';
import {
  CreateTaskDto,
  UpdateTaskDto,
  TaskResponseDto,
  GenerateTasksDto,
  TasksResponse,
} from '../dto/task.dto';
import { Task, TaskStatus } from '../../database/entities/task.entity';

@Injectable()
export class TaskService {
  constructor(
    private readonly taskRepository: TaskRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly prdRepository: PRDRepository,
  ) {}

  async create(createTaskDto: CreateTaskDto): Promise<TaskResponseDto> {
    const session = await this.sessionRepository.findOne(
      createTaskDto.sessionId,
    );
    if (!session) {
      throw new NotFoundException(`会话ID ${createTaskDto.sessionId} 不存在`);
    }

    const task = await this.taskRepository.create({
      ...createTaskDto,
      session,
    });

    return this.mapToResponseDto(task);
  }

  async findAll(sessionId?: string): Promise<TaskResponseDto[]> {
    const tasks = await this.taskRepository.findAll(sessionId);
    return tasks.map((task) => this.mapToResponseDto(task));
  }

  async findOne(id: string): Promise<TaskResponseDto> {
    const task = await this.taskRepository.findOne(id);
    if (!task) {
      throw new NotFoundException(`任务ID ${id} 不存在`);
    }

    return this.mapToResponseDto(task);
  }

  async update(
    id: string,
    updateTaskDto: UpdateTaskDto,
  ): Promise<TaskResponseDto> {
    const task = await this.taskRepository.findOne(id);
    if (!task) {
      throw new NotFoundException(`任务ID ${id} 不存在`);
    }

    const updatedTask = await this.taskRepository.update(id, updateTaskDto);
    return this.mapToResponseDto(updatedTask);
  }

  async remove(id: string): Promise<void> {
    const task = await this.taskRepository.findOne(id);
    if (!task) {
      throw new NotFoundException(`任务ID ${id} 不存在`);
    }

    await this.taskRepository.remove(id);
  }

  async generateTasks(
    generateTasksDto: GenerateTasksDto,
  ): Promise<TasksResponse> {
    const session = await this.sessionRepository.findOne(
      generateTasksDto.sessionId,
    );
    if (!session) {
      throw new NotFoundException(
        `会话ID ${generateTasksDto.sessionId} 不存在`,
      );
    }

    // 获取最新的PRD
    const latestPRD = await this.prdRepository.findLatestBySessionId(
      generateTasksDto.sessionId,
    );
    if (!latestPRD || !latestPRD.structuredContent) {
      throw new BadRequestException(
        '没有找到结构化的PRD内容，请先生成结构化PRD',
      );
    }

    // 这里应该调用LLM服务生成任务列表
    // 目前只是简单生成一些示例任务
    const sampleTasks = [
      {
        title: '用户认证模块开发',
        description: '实现用户登录、注册、权限管理功能',
        status: TaskStatus.NOT_STARTED,
        order: 1,
        dependencies: [],
      },
      {
        title: '文档编辑器核心功能',
        description: '实现基础的文本编辑、格式化功能',
        status: TaskStatus.NOT_STARTED,
        order: 2,
        dependencies: ['1'],
      },
      {
        title: '实时协作同步服务',
        description: '实现多用户实时编辑的数据同步',
        status: TaskStatus.NOT_STARTED,
        order: 3,
        dependencies: ['2'],
      },
      {
        title: '历史版本存储与回溯',
        description: '实现文档历史版本的存储和查看功能',
        status: TaskStatus.NOT_STARTED,
        order: 4,
        dependencies: ['2', '3'],
      },
    ];

    // 创建任务
    const createdTasks = [];
    for (const taskData of sampleTasks) {
      const task = await this.taskRepository.create({
        ...taskData,
        session,
      });
      createdTasks.push(this.mapToResponseDto(task));
    }

    return {
      tasks: createdTasks,
      message: '任务列表生成成功',
    };
  }

  async updateBatchStatus(
    sessionId: string,
    taskIds: string[],
    status: TaskStatus,
  ): Promise<TaskResponseDto[]> {
    const session = await this.sessionRepository.findOne(sessionId);
    if (!session) {
      throw new NotFoundException(`会话ID ${sessionId} 不存在`);
    }

    const updatedTasks = [];
    for (const taskId of taskIds) {
      const task = await this.taskRepository.findOne(taskId);
      if (task && task.session.id === sessionId) {
        const updatedTask = await this.taskRepository.update(taskId, { status });
        updatedTasks.push(this.mapToResponseDto(updatedTask));
      }
    }

    return updatedTasks;
  }

  async getTaskStatistics(sessionId: string): Promise<{
    total: number;
    notStarted: number;
    inProgress: number;
    completed: number;
  }> {
    const tasks = await this.taskRepository.findAll(sessionId);

    return {
      total: tasks.length,
      notStarted: tasks.filter(task => task.status === TaskStatus.NOT_STARTED).length,
      inProgress: tasks.filter(task => task.status === TaskStatus.IN_PROGRESS).length,
      completed: tasks.filter(task => task.status === TaskStatus.COMPLETED).length,
    };
  }

  async reorderTasks(sessionId: string, taskOrders: { id: string; order: number }[]): Promise<TaskResponseDto[]> {
    const session = await this.sessionRepository.findOne(sessionId);
    if (!session) {
      throw new NotFoundException(`会话ID ${sessionId} 不存在`);
    }

    const updatedTasks = [];
    for (const { id, order } of taskOrders) {
      const task = await this.taskRepository.findOne(id);
      if (task && task.session.id === sessionId) {
        const updatedTask = await this.taskRepository.update(id, { order });
        updatedTasks.push(this.mapToResponseDto(updatedTask));
      }
    }

    return updatedTasks.sort((a, b) => a.order - b.order);
  }

  private mapToResponseDto(task: Task): TaskResponseDto {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      status: task.status,
      order: task.order,
      dependencies: task.dependencies || [],
      tempData: task.tempData,
      sessionId: task.session?.id,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    };
  }
}
