import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { MessageRepository } from '../../database/repositories/message.repository';
import { SessionRepository } from '../../database/repositories/session.repository';
import {
  CreateMessageDto,
  UpdateMessageDto,
  MessageResponseDto,
  MessagesResponseDto,
} from './dto/message.dto';
import { Message } from '../../database/entities/message.entity';

@Injectable()
export class MessageService {
  constructor(
    private readonly messageRepository: MessageRepository,
    private readonly sessionRepository: SessionRepository,
  ) {}

  async create(createMessageDto: CreateMessageDto): Promise<MessageResponseDto> {
    // 验证threadId是否存在对应的会话
    const session = await this.sessionRepository.findByThreadId(createMessageDto.threadId);
    if (!session) {
      throw new BadRequestException(`线程ID ${createMessageDto.threadId} 不存在对应的会话`);
    }

    const message = await this.messageRepository.create(createMessageDto);
    return this.mapToResponseDto(message);
  }

  async findByThreadId(threadId: string): Promise<MessagesResponseDto> {
    const messages = await this.messageRepository.findByThreadId(threadId);
    const total = await this.messageRepository.countByThreadId(threadId);

    return {
      messages: messages.map(message => this.mapToResponseDto(message)),
      total,
      threadId,
    };
  }

  async findBySessionId(sessionId: string): Promise<MessagesResponseDto> {
    // 首先尝试直接作为threadId查询
    let messages = await this.messageRepository.findByThreadId(sessionId);
    let threadId = sessionId;

    // 如果没有找到消息，尝试从会话中查找threadId
    if (messages.length === 0) {
      const session = await this.sessionRepository.findOne(sessionId);
      if (session && session.threadId) {
        messages = await this.messageRepository.findByThreadId(session.threadId);
        threadId = session.threadId;
      }
    }

    const total = messages.length;

    return {
      messages: messages.map(message => this.mapToResponseDto(message)),
      total,
      threadId,
    };
  }

  async findOne(id: string): Promise<MessageResponseDto> {
    const message = await this.messageRepository.findOne(id);
    if (!message) {
      throw new NotFoundException(`消息ID ${id} 不存在`);
    }

    return this.mapToResponseDto(message);
  }

  async update(id: string, updateMessageDto: UpdateMessageDto): Promise<MessageResponseDto> {
    const message = await this.messageRepository.findOne(id);
    if (!message) {
      throw new NotFoundException(`消息ID ${id} 不存在`);
    }

    const updatedMessage = await this.messageRepository.update(id, updateMessageDto);
    return this.mapToResponseDto(updatedMessage);
  }

  async remove(id: string): Promise<void> {
    const message = await this.messageRepository.findOne(id);
    if (!message) {
      throw new NotFoundException(`消息ID ${id} 不存在`);
    }

    await this.messageRepository.remove(id);
  }

  async removeByThreadId(threadId: string): Promise<void> {
    await this.messageRepository.removeByThreadId(threadId);
  }

  private mapToResponseDto(message: Message): MessageResponseDto {
    return {
      id: message.id,
      threadId: message.threadId,
      role: message.role,
      agent: message.agent,
      content: message.content,
      toolCalls: message.toolCalls,
      options: message.options,
      finishReason: message.finishReason,
      interruptFeedback: message.interruptFeedback,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
    };
  }
}
