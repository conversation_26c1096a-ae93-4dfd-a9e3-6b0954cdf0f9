import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseService } from './database.service';
import { Message } from './entities/message.entity';
import { Session } from './entities/session.entity';
import { Task } from './entities/task.entity';
import { PRD } from './entities/prd.entity';
import { MessageRepository } from './repositories/message.repository';
import { SessionRepository } from './repositories/session.repository';
import { TaskRepository } from './repositories/task.repository';
import { PRDRepository } from './repositories/prd.repository';

/**
 * Planner模块的数据库模块
 *
 * 使用全局TypeOrmModule连接，只注册自己的实体
 */
@Module({
  imports: [
    // 只使用TypeOrmModule.forFeature注册实体，不创建新的数据库连接
    TypeOrmModule.forFeature([Message, Session, Task, PRD]),
  ],
  providers: [
    DatabaseService,
    MessageRepository,
    SessionRepository,
    TaskRepository,
    PRDRepository,
  ],
  exports: [
    DatabaseService,
    TypeOrmModule,
    MessageRepository,
    SessionRepository,
    TaskRepository,
    PRDRepository,
  ],
})
export class DatabaseModule {}
