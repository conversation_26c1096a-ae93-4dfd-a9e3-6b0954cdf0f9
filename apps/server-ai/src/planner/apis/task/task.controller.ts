import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { TaskService } from './task.service';
import {
  CreateTaskDto,
  UpdateTaskDto,
  TaskResponseDto,
  GenerateTasksDto,
  TasksResponse,
} from '../dto/task.dto';
import { TransformInterceptor } from '../../../utils/transform.interceptor';

@Controller('planner/tasks')
@UseInterceptors(TransformInterceptor)
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Post()
  async create(@Body() createTaskDto: CreateTaskDto): Promise<TaskResponseDto> {
    return this.taskService.create(createTaskDto);
  }

  @Get()
  async findAll(
    @Query('sessionId') sessionId?: string,
  ): Promise<TaskResponseDto[]> {
    return this.taskService.findAll(sessionId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<TaskResponseDto> {
    return this.taskService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
  ): Promise<TaskResponseDto> {
    return this.taskService.update(id, updateTaskDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.taskService.remove(id);
  }

  @Post('generate')
  async generateTasks(
    @Body() generateTasksDto: GenerateTasksDto,
  ): Promise<TasksResponse> {
    return this.taskService.generateTasks(generateTasksDto);
  }

  @Patch('batch-status')
  async updateBatchStatus(
    @Body() body: { sessionId: string; taskIds: string[]; status: string },
  ): Promise<TaskResponseDto[]> {
    return this.taskService.updateBatchStatus(body.sessionId, body.taskIds, body.status as any);
  }

  @Get('statistics/:sessionId')
  async getTaskStatistics(@Param('sessionId') sessionId: string) {
    return this.taskService.getTaskStatistics(sessionId);
  }

  @Patch('reorder')
  async reorderTasks(
    @Body() body: { sessionId: string; taskOrders: { id: string; order: number }[] },
  ): Promise<TaskResponseDto[]> {
    return this.taskService.reorderTasks(body.sessionId, body.taskOrders);
  }
}
