import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>t<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { MessageController } from './message.controller';
import { MessageService } from './message.service';
import { DatabaseModule } from '../../database/database.module';
import { GraphService } from '../../../plan/graph/graph.service';

@Module({
  imports: [DatabaseModule],
  controllers: [ChatController, MessageController],
  providers: [ChatService, MessageService, GraphService],
  exports: [ChatService, MessageService],
})
export class ChatModule {}
