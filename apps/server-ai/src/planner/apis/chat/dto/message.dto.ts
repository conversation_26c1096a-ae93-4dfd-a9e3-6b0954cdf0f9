import { IsNotEmpty, IsString, IsOptional, IsEnum, IsArray } from 'class-validator';
import { MessageRole, AgentType } from '../../../database/entities/message.entity';

export class GetMessagesDto {
  @IsNotEmpty()
  @IsString()
  sessionId: string;
}

export class CreateMessageDto {
  @IsNotEmpty()
  @IsString()
  threadId: string;

  @IsEnum(MessageRole)
  role: MessageRole;

  @IsOptional()
  @IsEnum(AgentType)
  agent?: AgentType;

  @IsNotEmpty()
  @IsString()
  content: string;

  @IsOptional()
  @IsArray()
  toolCalls?: any[];

  @IsOptional()
  @IsArray()
  options?: { text: string; value: string }[];

  @IsOptional()
  @IsString()
  finishReason?: string;

  @IsOptional()
  @IsString()
  interruptFeedback?: string;
}

export class UpdateMessageDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsArray()
  toolCalls?: any[];

  @IsOptional()
  @IsArray()
  options?: { text: string; value: string }[];

  @IsOptional()
  @IsString()
  finishReason?: string;

  @IsOptional()
  @IsString()
  interruptFeedback?: string;
}

export class MessageResponseDto {
  id: string;
  threadId: string;
  role: MessageRole;
  agent?: AgentType;
  content: string;
  toolCalls?: any[];
  options?: { text: string; value: string }[];
  finishReason?: string;
  interruptFeedback?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class MessagesResponseDto {
  messages: MessageResponseDto[];
  total: number;
  threadId: string;
}
