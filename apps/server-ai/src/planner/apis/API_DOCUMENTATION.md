# Planner API 接口文档

## 概述

本文档描述了 Planner 模块的所有后端 API 接口，包括会话管理、消息处理和任务管理功能。

## 基础路径

所有 API 接口的基础路径为：`/api/planner`

## 1. 会话管理 API (`/sessions`)

### 1.1 创建会话
- **路径**: `POST /sessions`
- **描述**: 创建新的会话
- **请求体**:
```json
{
  "title": "会话标题",
  "threadId": "可选的线程ID"
}
```
- **响应**:
```json
{
  "id": "会话ID",
  "title": "会话标题",
  "status": "requirement_analysis",
  "threadId": "线程ID",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 1.2 获取会话列表
- **路径**: `GET /sessions`
- **描述**: 获取所有会话列表
- **响应**: 会话数组

### 1.3 获取会话详情
- **路径**: `GET /sessions/:id`
- **描述**: 获取指定会话的详细信息，包含关联的任务和PRD
- **响应**: 包含 tasks 和 prds 数组的会话详情

### 1.4 根据线程ID获取会话
- **路径**: `GET /sessions/thread/:threadId`
- **描述**: 根据线程ID获取会话详情

### 1.5 更新会话
- **路径**: `PATCH /sessions/:id`
- **描述**: 更新会话信息
- **请求体**:
```json
{
  "title": "新标题",
  "status": "in_development",
  "frontendPageId": "前端页面ID",
  "backendApiId": "后端API ID",
  "previewUrl": "预览链接"
}
```

### 1.6 删除会话
- **路径**: `DELETE /sessions/:id`
- **描述**: 软删除指定会话

## 2. 消息管理 API (`/messages`)

### 2.1 创建消息
- **路径**: `POST /messages`
- **描述**: 创建新消息
- **请求体**:
```json
{
  "threadId": "线程ID",
  "role": "user|assistant|tool",
  "agent": "coordinator|planner|researcher|coder|analyst|reporter",
  "content": "消息内容",
  "toolCalls": [],
  "options": [{"text": "选项文本", "value": "选项值"}]
}
```

### 2.2 获取会话消息
- **路径**: `GET /messages?sessionId=会话ID`
- **描述**: 获取指定会话的所有消息
- **响应**:
```json
{
  "messages": [],
  "total": 10,
  "threadId": "线程ID"
}
```

### 2.3 根据线程ID获取消息
- **路径**: `GET /messages/thread/:threadId`
- **描述**: 直接根据线程ID获取消息

### 2.4 获取单个消息
- **路径**: `GET /messages/:id`
- **描述**: 获取指定消息详情

### 2.5 更新消息
- **路径**: `PATCH /messages/:id`
- **描述**: 更新消息内容

### 2.6 删除消息
- **路径**: `DELETE /messages/:id`
- **描述**: 删除指定消息

### 2.7 删除线程所有消息
- **路径**: `DELETE /messages/thread/:threadId`
- **描述**: 删除指定线程的所有消息

## 3. 任务管理 API (`/tasks`)

### 3.1 创建任务
- **路径**: `POST /tasks`
- **描述**: 创建新任务
- **请求体**:
```json
{
  "title": "任务标题",
  "description": "任务描述",
  "status": "not_started|in_progress|completed",
  "order": 1,
  "dependencies": ["依赖任务ID"],
  "sessionId": "会话ID"
}
```

### 3.2 获取任务列表
- **路径**: `GET /tasks?sessionId=会话ID`
- **描述**: 获取任务列表，可按会话ID筛选

### 3.3 获取任务详情
- **路径**: `GET /tasks/:id`
- **描述**: 获取指定任务详情

### 3.4 更新任务
- **路径**: `PATCH /tasks/:id`
- **描述**: 更新任务信息

### 3.5 删除任务
- **路径**: `DELETE /tasks/:id`
- **描述**: 删除指定任务

### 3.6 生成任务列表
- **路径**: `POST /tasks/generate`
- **描述**: 基于PRD自动生成任务列表
- **请求体**:
```json
{
  "sessionId": "会话ID"
}
```

### 3.7 批量更新任务状态
- **路径**: `PATCH /tasks/batch-status`
- **描述**: 批量更新多个任务的状态
- **请求体**:
```json
{
  "sessionId": "会话ID",
  "taskIds": ["任务ID1", "任务ID2"],
  "status": "completed"
}
```

### 3.8 获取任务统计
- **路径**: `GET /tasks/statistics/:sessionId`
- **描述**: 获取指定会话的任务统计信息
- **响应**:
```json
{
  "total": 10,
  "notStarted": 5,
  "inProgress": 3,
  "completed": 2
}
```

### 3.9 重新排序任务
- **路径**: `PATCH /tasks/reorder`
- **描述**: 重新排序任务
- **请求体**:
```json
{
  "sessionId": "会话ID",
  "taskOrders": [
    {"id": "任务ID1", "order": 1},
    {"id": "任务ID2", "order": 2}
  ]
}
```

## 4. 状态码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 5. 错误响应格式

```json
{
  "statusCode": 400,
  "message": "错误描述",
  "error": "Bad Request"
}
```

## 6. 数据模型

### 会话状态 (SessionStatus)
- `requirement_analysis`: 需求分析阶段
- `task_planning`: 任务规划阶段
- `pending_development`: 待开发阶段
- `in_development`: 开发中阶段
- `completed`: 已完成阶段
- `failed`: 失败状态
- `archived`: 已归档阶段

### 消息角色 (MessageRole)
- `user`: 用户消息
- `assistant`: AI助手消息
- `tool`: 工具调用消息

### 任务状态 (TaskStatus)
- `not_started`: 未开始
- `in_progress`: 进行中
- `completed`: 已完成
