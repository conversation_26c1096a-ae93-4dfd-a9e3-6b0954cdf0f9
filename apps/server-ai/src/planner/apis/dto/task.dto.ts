import { TaskStatus } from '../../database/entities/task.entity';

export class CreateTaskDto {
  title: string;
  description: string;
  status?: TaskStatus;
  order?: number;
  dependencies?: string[];
  sessionId: string;
}

export class UpdateTaskDto {
  title?: string;
  description?: string;
  status?: TaskStatus;
  order?: number;
  dependencies?: string[];
  tempData?: string;
}

export class GenerateTasksDto {
  sessionId: string;
}

export class TaskResponseDto {
  id: string;
  title: string;
  description: string;
  status: TaskStatus;
  order: number;
  dependencies: string[];
  tempData?: string;
  sessionId: string;
  createdAt: Date;
  updatedAt: Date;
}

export class TasksResponse {
  tasks: TaskResponseDto[];
  message: string;
}

export class BatchUpdateStatusDto {
  sessionId: string;
  taskIds: string[];
  status: TaskStatus;
}

export class ReorderTasksDto {
  sessionId: string;
  taskOrders: { id: string; order: number }[];
}

export class TaskStatisticsDto {
  total: number;
  notStarted: number;
  inProgress: number;
  completed: number;
}
