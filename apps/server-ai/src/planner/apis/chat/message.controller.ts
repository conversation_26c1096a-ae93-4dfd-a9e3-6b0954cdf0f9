import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { MessageService } from './message.service';
import {
  GetMessagesDto,
  CreateMessageDto,
  UpdateMessageDto,
  MessageResponseDto,
  MessagesResponseDto,
} from './dto/message.dto';
import { TransformInterceptor } from '../../../utils/transform.interceptor';

@Controller('planner/messages')
@UseInterceptors(TransformInterceptor)
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  /**
   * 创建新消息
   */
  @Post()
  async create(@Body() createMessageDto: CreateMessageDto): Promise<MessageResponseDto> {
    return this.messageService.create(createMessageDto);
  }

  /**
   * 获取会话的所有消息
   */
  @Get()
  async getMessages(@Query() query: GetMessagesDto): Promise<MessagesResponseDto> {
    return this.messageService.findBySessionId(query.sessionId);
  }

  /**
   * 根据线程ID获取消息
   */
  @Get('thread/:threadId')
  async getMessagesByThreadId(@Param('threadId') threadId: string): Promise<MessagesResponseDto> {
    return this.messageService.findByThreadId(threadId);
  }

  /**
   * 获取单个消息详情
   */
  @Get(':id')
  async findOne(@Param('id') id: string): Promise<MessageResponseDto> {
    return this.messageService.findOne(id);
  }

  /**
   * 更新消息
   */
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateMessageDto: UpdateMessageDto,
  ): Promise<MessageResponseDto> {
    return this.messageService.update(id, updateMessageDto);
  }

  /**
   * 删除消息
   */
  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.messageService.remove(id);
  }

  /**
   * 删除线程的所有消息
   */
  @Delete('thread/:threadId')
  async removeByThreadId(@Param('threadId') threadId: string): Promise<void> {
    return this.messageService.removeByThreadId(threadId);
  }
}
