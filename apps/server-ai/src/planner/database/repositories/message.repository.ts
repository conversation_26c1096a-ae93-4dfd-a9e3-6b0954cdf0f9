import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from '../entities/message.entity';

@Injectable()
export class MessageRepository {
  constructor(
    @InjectRepository(Message)
    private readonly repository: Repository<Message>,
  ) {}

  async create(data: Partial<Message>): Promise<Message> {
    const message = this.repository.create(data);
    return this.repository.save(message);
  }

  async findByThreadId(threadId: string): Promise<Message[]> {
    return this.repository.find({
      where: { threadId },
      order: { createdAt: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Message> {
    return this.repository.findOne({
      where: { id },
    });
  }

  async findAll(): Promise<Message[]> {
    return this.repository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async update(id: string, data: Partial<Message>): Promise<Message> {
    await this.repository.update(id, data);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async removeByThreadId(threadId: string): Promise<void> {
    await this.repository.delete({ threadId });
  }

  async countByThreadId(threadId: string): Promise<number> {
    return this.repository.count({
      where: { threadId },
    });
  }
}
