### Planner API 测试文件
### 使用 REST Client 扩展或类似工具测试这些接口

@baseUrl = http://localhost:3000/api/planner

### 1. 创建会话
POST {{baseUrl}}/sessions
Content-Type: application/json

{
  "title": "测试会话 - 文档编辑器项目"
}

### 2. 获取会话列表
GET {{baseUrl}}/sessions

### 3. 获取会话详情 (需要替换为实际的会话ID)
GET {{baseUrl}}/sessions/{{sessionId}}

### 4. 创建消息
POST {{baseUrl}}/messages
Content-Type: application/json

{
  "threadId": "{{threadId}}",
  "role": "user",
  "content": "我想开发一个在线文档编辑器，类似于Google Docs，需要支持实时协作功能。"
}

### 5. 获取消息列表
GET {{baseUrl}}/messages?sessionId={{sessionId}}

### 6. 生成任务列表
POST {{baseUrl}}/tasks/generate
Content-Type: application/json

{
  "sessionId": "{{sessionId}}"
}

### 7. 获取任务列表
GET {{baseUrl}}/tasks?sessionId={{sessionId}}

### 8. 获取任务统计
GET {{baseUrl}}/tasks/statistics/{{sessionId}}

### 9. 更新任务状态
PATCH {{baseUrl}}/tasks/{{taskId}}
Content-Type: application/json

{
  "status": "in_progress"
}

### 10. 批量更新任务状态
PATCH {{baseUrl}}/tasks/batch-status
Content-Type: application/json

{
  "sessionId": "{{sessionId}}",
  "taskIds": ["{{taskId1}}", "{{taskId2}}"],
  "status": "completed"
}

### 11. 重新排序任务
PATCH {{baseUrl}}/tasks/reorder
Content-Type: application/json

{
  "sessionId": "{{sessionId}}",
  "taskOrders": [
    {"id": "{{taskId1}}", "order": 1},
    {"id": "{{taskId2}}", "order": 2}
  ]
}

### 12. 更新会话状态
PATCH {{baseUrl}}/sessions/{{sessionId}}
Content-Type: application/json

{
  "status": "in_development",
  "previewUrl": "https://example.com/preview"
}

### 13. 删除任务
DELETE {{baseUrl}}/tasks/{{taskId}}

### 14. 删除消息
DELETE {{baseUrl}}/messages/{{messageId}}

### 15. 删除会话 (软删除)
DELETE {{baseUrl}}/sessions/{{sessionId}}
